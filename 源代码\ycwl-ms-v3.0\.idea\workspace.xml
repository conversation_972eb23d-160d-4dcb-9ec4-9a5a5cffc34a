<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3bd2477c-9aff-4bff-b16d-d6cdc034af5e" name="更改" comment="聚集区打卡点修复完成（真）">
      <change beforePath="$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/controller/ClusterController.java" beforeDir="false" afterPath="$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/controller/ClusterController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/mapper/StoreMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/mapper/StoreMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/pojo/ListErrorPointSon.java" beforeDir="false" afterPath="$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/pojo/ListErrorPointSon.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/PointService.java" beforeDir="false" afterPath="$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/PointService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/src/main/resources/mapper/StoreMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/cluster-calculate/src/main/resources/mapper/StoreMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/target/app.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/target/app.jar.original" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/target/classes/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/cluster-calculate/target/classes/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/controller/ClusterController.class" beforeDir="false" afterPath="$PROJECT_DIR$/cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/controller/ClusterController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/mapper/StoreMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/mapper/StoreMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/ListErrorPointSon.class" beforeDir="false" afterPath="$PROJECT_DIR$/cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/ListErrorPointSon.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/PointService.class" beforeDir="false" afterPath="$PROJECT_DIR$/cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/PointService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/target/classes/mapper/StoreMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/cluster-calculate/target/classes/mapper/StoreMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/target/maven-archiver/pom.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/cluster-calculate/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/cluster-calculate/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/createdFiles.lst" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cluster-calculate/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/inputFiles.lst" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/user-service/target/classes/com/ict/ycwl/user/service/impl/RoleServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/user-service/target/classes/com/ict/ycwl/user/service/impl/RoleServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/user-service/target/classes/com/ict/ycwl/user/service/impl/UserServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/user-service/target/classes/com/ict/ycwl/user/service/impl/UserServiceImpl.class" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="JUnit5 Test Class" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/data-management/target/app.jar!/BOOT-INF/classes/mapper/CarMapper.xml" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="D:/maven/apache-maven-3.6.3" />
        <option name="showDialogWithAdvancedSettings" value="true" />
        <option name="userSettingsFile" value="D:\maven\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="disabledProfiles">
      <list>
        <option value="development" />
      </list>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="jreName" value="1.8 (2)" />
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2mvV0xk9HrVo4mmWzPRpimDtwZl" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_ADD_EXTERNAL_FILES&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrary&quot;: &quot;JUnit5&quot;,
    &quot;com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrarySuperClass.JUnit5&quot;: &quot;&quot;,
    &quot;database.data.extractors.current.export.id&quot;: &quot;Markdown-Groovy.md.groovy&quot;,
    &quot;deletionFromPopupRequiresConfirmation&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;jdk.selected.JAVA_MODULE&quot;: &quot;1.8&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/烟草/交接信息/源代码/ycwl-ms-v3.0/simple_accumulation_check.sql&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;SDK&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.424626&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;99a8ba2511bfe88f7ebc6ab27cea7821&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\AAbiji2024\YCV3\ycwl-ms-v3.0\path-calculate" />
      <recent name="E:\AAbiji2024\YCV3\ycwl-ms-v3.0\user-service\src\main\java\com\ict\ycwl\user\utils" />
      <recent name="E:\AAbiji2024\YCV3\ycwl-ms-v3.0\user-service\src\main\java\com\ict\ycwl\user" />
      <recent name="E:\AAbiji2024\YCV3\ycwl-ms-v3.0\pickup\src\main\java\com\ict\ycwl\pickup\utils" />
      <recent name="E:\AAbiji2024\YCV3\ycwl-ms-v3.0\pickup\src\main\java\com\ict\ycwl\pickup" />
    </key>
    <key name="CreateTestDialog.Recents.Supers">
      <recent name="" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\AAbiji2024\YCV3\ycwl-ms-v3.0\path-calculate\src\main\resources" />
      <recent name="E:\AAbiji2024\YCV3\ycwl-ms-v3.0\path-calculate\src\main\resources\mapper" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.ict.ycwl.clustercalculate.utlis" />
      <recent name="com.ict.ycwl.clustercalculate.pojo" />
      <recent name="com.ict.ycwl.clustercalculate" />
      <recent name="com.ict.ycwl.pathcalculate.service.Impl" />
      <recent name="com.ict.ycwl.pathcalculate.service" />
    </key>
    <key name="CreateTestDialog.RecentsKey">
      <recent name="com.ict.ycwl.pathcalculate.utils" />
      <recent name="com.ict.ycwl.clustercalculate.service.Impl" />
      <recent name="com.ict.ycwl.pathcalculate.service.Impl" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.ict.ycwl.guestbook.filter" />
      <recent name="com.ict.ycwl.pathcalculate.AOP" />
      <recent name="com.ict.ycwl.clustercalculate" />
      <recent name="com.ict.ycwl.clustercalculate.pojo" />
      <recent name="com.ict.ycwl.clustercalculate.mapper" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.DataManagementApplication">
    <configuration name="RouteTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="pathcalculate" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ict.ycwl.pathcalculate.route.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ict.ycwl.pathcalculate.route" />
      <option name="MAIN_CLASS_NAME" value="com.ict.ycwl.pathcalculate.route.RouteTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RouteTest001.test06" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="pathcalculate" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ict.ycwl.pathcalculate.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ict.ycwl.pathcalculate" />
      <option name="MAIN_CLASS_NAME" value="com.ict.ycwl.pathcalculate.RouteTest001" />
      <option name="METHOD_NAME" value="test06" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RouteTest001.test07" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="pathcalculate" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ict.ycwl.pathcalculate.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ict.ycwl.pathcalculate" />
      <option name="MAIN_CLASS_NAME" value="com.ict.ycwl.pathcalculate.RouteTest001" />
      <option name="METHOD_NAME" value="test07" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ClusterCalculateApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="clustercalculate" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.clustercalculate.ClusterCalculateApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DataManagementApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8 (2)" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="data-management" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.datamanagement.DataManagementApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="gateway" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.gateway.GatewayApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ict.ycwl.gateway.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GuestbookApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="guestbook" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.guestbook.GuestbookApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PathCalculateApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="pathcalculate" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.pathcalculate.PathCalculateApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PickupServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="pickup" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.pickup.PickupServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserServiceApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="user-service" />
      <option name="PROGRAM_PARAMETERS" value="-Dserver.port=8081" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.user.UserServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="user-service" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.user.UserServiceApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ict.ycwl.user.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8 (2)" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <module name="user-service" />
      <option name="PROGRAM_PARAMETERS" value="-Dserver.port=8081" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ict.ycwl.user.UserServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="JUnit.RouteTest001.test06" />
      <item itemvalue="JUnit.RouteTest001.test07" />
      <item itemvalue="JUnit.RouteTest" />
      <item itemvalue="Spring Boot.ClusterCalculateApplication" />
      <item itemvalue="Spring Boot.DataManagementApplication" />
      <item itemvalue="Spring Boot.GuestbookApplication" />
      <item itemvalue="Spring Boot.PathCalculateApplication" />
      <item itemvalue="Spring Boot.PickupServiceApplication" />
      <item itemvalue="Spring Boot.UserServiceApplication (1)" />
      <item itemvalue="Spring Boot.GatewayApplication" />
      <item itemvalue="Spring Boot.UserServiceApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.GatewayApplication" />
        <item itemvalue="Spring Boot.UserServiceApplication" />
        <item itemvalue="JUnit.RouteTest001.test06" />
        <item itemvalue="JUnit.RouteTest" />
        <item itemvalue="JUnit.RouteTest001.test07" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="3bd2477c-9aff-4bff-b16d-d6cdc034af5e" name="更改" comment="" />
      <created>1712155953319</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1712155953319</updated>
      <workItem from="1727956924120" duration="6492000" />
      <workItem from="1728009894252" duration="2923000" />
      <workItem from="1728025096680" duration="10912000" />
      <workItem from="1728091366135" duration="46567000" />
      <workItem from="1728384561877" duration="24327000" />
      <workItem from="1728517371435" duration="1598000" />
      <workItem from="1728518990669" duration="6063000" />
      <workItem from="1728606316375" duration="31376000" />
      <workItem from="1728735541893" duration="6015000" />
      <workItem from="1728782604798" duration="53879000" />
      <workItem from="1729061232028" duration="7501000" />
      <workItem from="1729083289705" duration="1110000" />
      <workItem from="1729122750011" duration="5959000" />
      <workItem from="1729170569043" duration="5293000" />
      <workItem from="1729210876155" duration="13214000" />
      <workItem from="1729241464291" duration="13872000" />
      <workItem from="1729301288860" duration="55826000" />
      <workItem from="1729481458987" duration="4220000" />
      <workItem from="1729509760565" duration="52896000" />
      <workItem from="1729900262002" duration="33336000" />
      <workItem from="1730001598471" duration="31757000" />
      <workItem from="1730084352115" duration="4814000" />
      <workItem from="1730099235784" duration="19563000" />
      <workItem from="1730176288488" duration="797000" />
      <workItem from="1730177360052" duration="1378000" />
      <workItem from="1730178898907" duration="866000" />
      <workItem from="1730187322587" duration="13344000" />
      <workItem from="1730272299763" duration="62879000" />
      <workItem from="1730513446254" duration="22285000" />
      <workItem from="1730596155611" duration="40502000" />
      <workItem from="1730815338347" duration="18963000" />
      <workItem from="1730963258643" duration="59223000" />
      <workItem from="1731215227001" duration="39472000" />
      <workItem from="1731407466747" duration="17606000" />
      <workItem from="1731484944818" duration="17871000" />
      <workItem from="1731550283936" duration="874000" />
      <workItem from="1731583058080" duration="44445000" />
      <workItem from="1731690880321" duration="2981000" />
      <workItem from="1731694047934" duration="322000" />
      <workItem from="1731694401042" duration="2647000" />
      <workItem from="1731697133369" duration="27828000" />
      <workItem from="1731848271632" duration="2915000" />
      <workItem from="1731858634289" duration="23000" />
      <workItem from="1731927400644" duration="38403000" />
      <workItem from="1732191545642" duration="7695000" />
      <workItem from="1732201057000" duration="25326000" />
      <workItem from="1732456431714" duration="474000" />
      <workItem from="1732518434267" duration="26729000" />
      <workItem from="1732618622584" duration="9828000" />
      <workItem from="1732670187637" duration="26149000" />
      <workItem from="1732799882970" duration="177000" />
      <workItem from="1732800071884" duration="3316000" />
      <workItem from="1732841596078" duration="1383000" />
      <workItem from="1732843535742" duration="26989000" />
      <workItem from="1732891794663" duration="25000" />
      <workItem from="1732967659953" duration="11459000" />
      <workItem from="1732983675174" duration="3417000" />
      <workItem from="1733013310357" duration="59940000" />
      <workItem from="1733156471436" duration="50823000" />
      <workItem from="1733357045796" duration="28987000" />
      <workItem from="1733444762608" duration="21693000" />
      <workItem from="1733546580500" duration="23395000" />
      <workItem from="1733663130694" duration="12501000" />
      <workItem from="1733728283477" duration="7392000" />
      <workItem from="1733741798452" duration="7937000" />
      <workItem from="1733766005035" duration="602000" />
      <workItem from="1733834612020" duration="654000" />
      <workItem from="1733904786683" duration="7000" />
      <workItem from="1734362895311" duration="77000" />
      <workItem from="1734363063916" duration="618000" />
      <workItem from="1734426647928" duration="2726000" />
      <workItem from="1734437784457" duration="2755000" />
      <workItem from="1734492229425" duration="2349000" />
      <workItem from="1734926521971" duration="7097000" />
      <workItem from="1735179146521" duration="1610000" />
      <workItem from="1735286137486" duration="251000" />
      <workItem from="1735966131744" duration="2413000" />
      <workItem from="1736043542621" duration="730000" />
      <workItem from="1736044591902" duration="3930000" />
      <workItem from="1736396233156" duration="37000" />
      <workItem from="1736473923131" duration="1378000" />
      <workItem from="1736946109307" duration="39000" />
      <workItem from="1737595894419" duration="13598000" />
      <workItem from="1737768144699" duration="252000" />
      <workItem from="1737768500707" duration="45000" />
      <workItem from="1737768850478" duration="309000" />
      <workItem from="1738131227913" duration="646000" />
      <workItem from="1738379674101" duration="4521000" />
      <workItem from="1738546876842" duration="1811000" />
      <workItem from="1738637084958" duration="5184000" />
      <workItem from="1738656424669" duration="1021000" />
      <workItem from="1738819817611" duration="851000" />
      <workItem from="1738899000446" duration="25428000" />
      <workItem from="1739070199883" duration="48493000" />
      <workItem from="1739412662659" duration="59162000" />
      <workItem from="1739673420929" duration="47330000" />
      <workItem from="1740019906393" duration="142000" />
      <workItem from="1740020946720" duration="1745000" />
      <workItem from="1740052325004" duration="3579000" />
      <workItem from="1740102366491" duration="8983000" />
      <workItem from="1740189709634" duration="6247000" />
      <workItem from="1740278262416" duration="783000" />
      <workItem from="1740312100607" duration="3152000" />
      <workItem from="1740376296539" duration="8469000" />
      <workItem from="1740400196326" duration="6705000" />
      <workItem from="1740492111788" duration="22000" />
      <workItem from="1740531357497" duration="54363000" />
      <workItem from="1740802094933" duration="66000" />
      <workItem from="1740802173115" duration="15493000" />
      <workItem from="1741000371117" duration="7270000" />
      <workItem from="1741007698844" duration="5675000" />
      <workItem from="1741074450032" duration="726000" />
      <workItem from="1741088107551" duration="51093000" />
      <workItem from="1741342738741" duration="16011000" />
      <workItem from="1741396618126" duration="31971000" />
      <workItem from="1741507072025" duration="26898000" />
      <workItem from="1741703516259" duration="23966000" />
      <workItem from="1741824767236" duration="9983000" />
      <workItem from="1741867641188" duration="1038000" />
      <workItem from="1741917953761" duration="9481000" />
      <workItem from="1742102274296" duration="11481000" />
      <workItem from="1742115144579" duration="6000" />
      <workItem from="1742176424065" duration="379000" />
      <workItem from="1742176984744" duration="22128000" />
      <workItem from="1742297230298" duration="86474000" />
      <workItem from="1742820911383" duration="49698000" />
      <workItem from="1743039820460" duration="205000" />
      <workItem from="1743040329550" duration="622000" />
      <workItem from="1743150217715" duration="4967000" />
      <workItem from="1743332659137" duration="1355000" />
      <workItem from="1743335613021" duration="9821000" />
      <workItem from="1743421071092" duration="219000" />
      <workItem from="1743421308625" duration="9608000" />
      <workItem from="1743473430577" duration="53236000" />
      <workItem from="1743985805936" duration="48775000" />
      <workItem from="1744262266304" duration="19870000" />
      <workItem from="1744614710708" duration="373000" />
      <workItem from="1744724410247" duration="7415000" />
      <workItem from="1744797528381" duration="5285000" />
      <workItem from="1744878083020" duration="411000" />
      <workItem from="1745025361367" duration="28875000" />
      <workItem from="1745916114936" duration="2000" />
      <workItem from="1745931314825" duration="41000" />
      <workItem from="1746017381450" duration="330000" />
      <workItem from="1746599298452" duration="732000" />
      <workItem from="1746600914592" duration="24625000" />
      <workItem from="1746942882327" duration="37569000" />
      <workItem from="1747274402650" duration="38913000" />
      <workItem from="1748145006844" duration="16876000" />
      <workItem from="1748413419486" duration="493000" />
      <workItem from="1748415674013" duration="1544000" />
      <workItem from="1748435916766" duration="5164000" />
      <workItem from="1748441174468" duration="733000" />
      <workItem from="1748441922954" duration="3303000" />
      <workItem from="1748477627723" duration="24271000" />
      <workItem from="1748931121401" duration="1145000" />
      <workItem from="1749020830609" duration="221000" />
      <workItem from="1749106978131" duration="88000" />
      <workItem from="1749107085500" duration="35804000" />
      <workItem from="1749775727210" duration="29937000" />
      <workItem from="1750330557175" duration="77356000" />
      <workItem from="1750917807267" duration="110000" />
      <workItem from="1751006752172" duration="9182000" />
      <workItem from="1751078521155" duration="638000" />
      <workItem from="1751289102876" duration="5846000" />
      <workItem from="1751340550062" duration="3559000" />
      <workItem from="1751368027691" duration="7831000" />
      <workItem from="1751418007313" duration="2700000" />
      <workItem from="1751436197806" duration="14045000" />
      <workItem from="1751504261401" duration="2546000" />
      <workItem from="1751547755367" duration="1163000" />
      <workItem from="1751634173510" duration="80989000" />
      <workItem from="1751852355693" duration="14894000" />
      <workItem from="1751940408403" duration="10023000" />
      <workItem from="1751983139401" duration="3913000" />
      <workItem from="1752026723945" duration="40000" />
      <workItem from="1752026777554" duration="58000" />
      <workItem from="1752026852512" duration="12867000" />
      <workItem from="1752064395492" duration="4546000" />
      <workItem from="1752113857519" duration="18677000" />
      <workItem from="1752156259773" duration="84000" />
      <workItem from="1752156364429" duration="1604000" />
      <workItem from="1752196198102" duration="19240000" />
      <workItem from="1752392713988" duration="16418000" />
      <workItem from="1752454921099" duration="18292000" />
      <workItem from="1752541885878" duration="19839000" />
      <workItem from="1752580500112" duration="6216000" />
      <workItem from="1752628551741" duration="8820000" />
      <workItem from="1752646039328" duration="6896000" />
      <workItem from="1752673871147" duration="353000" />
      <workItem from="1752716571441" duration="16559000" />
      <workItem from="1752743953867" duration="3731000" />
      <workItem from="1752803851662" duration="33000" />
      <workItem from="1752809273604" duration="20822000" />
      <workItem from="1752986121148" duration="7743000" />
      <workItem from="1753146665914" duration="718000" />
      <workItem from="1753172084498" duration="3842000" />
      <workItem from="1753209657751" duration="776000" />
      <workItem from="1753252055033" duration="8020000" />
      <workItem from="1753339906174" duration="11549000" />
      <workItem from="1753355160819" duration="2895000" />
      <workItem from="1753509140323" duration="3052000" />
      <workItem from="1753512325510" duration="12502000" />
      <workItem from="1753525532759" duration="3460000" />
      <workItem from="1753529145881" duration="1350000" />
      <workItem from="1753530506942" duration="2448000" />
      <workItem from="1753546818236" duration="13241000" />
      <workItem from="1753588978507" duration="3477000" />
      <workItem from="1753677345268" duration="12359000" />
      <workItem from="1753699431779" duration="2961000" />
      <workItem from="1753721667757" duration="7651000" />
      <workItem from="1753730861501" duration="125000" />
      <workItem from="1753762689887" duration="4233000" />
      <workItem from="1753860895691" duration="1196000" />
      <workItem from="1753874501354" duration="3364000" />
      <workItem from="1753891842113" duration="317000" />
      <workItem from="1753892392531" duration="170000" />
      <workItem from="1753892771706" duration="7242000" />
      <workItem from="1753909746797" duration="5071000" />
      <workItem from="1753924773625" duration="2000" />
      <workItem from="1753945307029" duration="8237000" />
      <workItem from="1753958515585" duration="3895000" />
      <workItem from="1753962525796" duration="2225000" />
      <workItem from="1754039611750" duration="22274000" />
      <workItem from="1754109883875" duration="950000" />
      <workItem from="1754205999261" duration="14598000" />
      <workItem from="1754239694731" duration="2619000" />
    </task>
    <task id="LOCAL-00001" summary="初次推送项目到github">
      <created>1737768996003</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1737768996004</updated>
    </task>
    <task id="LOCAL-00002" summary="创建顶点取货模块，并创建取货列表实体类">
      <created>1738641812575</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1738641812575</updated>
    </task>
    <task id="LOCAL-00003" summary="定点取货功能基本已经完成，但可能还存在一个bug待修复">
      <created>1740020020196</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1740020020196</updated>
    </task>
    <task id="LOCAL-00004" summary="定点取货功能基本已经完成，但可能还存在一个bug待修复">
      <created>1740020042218</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1740020042218</updated>
    </task>
    <task id="LOCAL-00005" summary="修复大量bug">
      <created>1740555126155</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1740555126155</updated>
    </task>
    <task id="LOCAL-00006" summary="完成大部分班组内路径优化，但是工作时长本来较长的班组的优化结果部分路线的工作时长会超过8小时">
      <created>1741678896199</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1741678896199</updated>
    </task>
    <task id="LOCAL-00007" summary="等待甲方验收版本，但是仍然存在部分bug">
      <created>1743214912983</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1743214912983</updated>
    </task>
    <task id="LOCAL-00008" summary="代码进行初步的sql优化，计算能正常完成，但仍需要较长的时间">
      <created>1743517517901</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1743517517901</updated>
    </task>
    <task id="LOCAL-00009" summary="基本上没有什么bug">
      <created>1745124769108</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1745124769108</updated>
    </task>
    <task id="LOCAL-00010" summary="基本上没有什么bug">
      <created>1745124859404</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1745124859404</updated>
    </task>
    <task id="LOCAL-00011" summary="引入数据库管理工具liquibase前进行备份">
      <created>1748137399343</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1748137399343</updated>
    </task>
    <task id="LOCAL-00012" summary="增加了商铺列表中的状态字段">
      <created>1748444057715</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1748444057715</updated>
    </task>
    <task id="LOCAL-00013" summary="实现版本控制之前进行数据库备份">
      <created>1749110004033</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1749110004033</updated>
    </task>
    <task id="LOCAL-00014" summary="引入版本控制前进行备份">
      <created>1749692987923</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1749692987923</updated>
    </task>
    <task id="LOCAL-00015" summary="存在循环依赖问题">
      <created>1749696597971</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1749696597971</updated>
    </task>
    <task id="LOCAL-00016" summary="实现版本控制功能，已经完成大部分版本管理功能">
      <created>1750310115406</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1750310115406</updated>
    </task>
    <task id="LOCAL-00017" summary="实现凸包不会冲突，但是，工作时长还是不够均衡">
      <created>1750660957655</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1750660957655</updated>
    </task>
    <task id="LOCAL-00018" summary="中转站一能实现挺好的班组均衡，但有一点点凸包冲突，整体上不错，但是中转站2效果很差">
      <created>1750917897462</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1750917897462</updated>
    </task>
    <task id="LOCAL-00019" summary="引入mysqlBaits多数据源前进行备份">
      <created>1751293264347</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1751293264347</updated>
    </task>
    <task id="LOCAL-00020" summary="通过mybatisPlus实现多数据源切换，通过aop自定义注解读取本地文件源实现">
      <created>1751460866340</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1751460866340</updated>
    </task>
    <task id="LOCAL-00021" summary="完成另存为新版本功能">
      <created>1751691542791</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1751691542791</updated>
    </task>
    <task id="LOCAL-00022" summary="完善了导出路径的商铺增加修改状态">
      <created>1751714176409</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1751714176409</updated>
    </task>
    <task id="LOCAL-00023" summary="完成了另存为新版本，已经一些bug修复，部署了聚集区模块的多数据库，pickup模块，data模块的多数据库">
      <created>1751877991874</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1751877991874</updated>
    </task>
    <task id="LOCAL-00024" summary="新增版本间的差异检测">
      <created>1752048934573</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1752048934573</updated>
    </task>
    <task id="LOCAL-00025" summary="交接路径计算">
      <created>1752646905554</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1752646905554</updated>
    </task>
    <task id="LOCAL-00026" summary="基本无bug">
      <created>1752745247163</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1752745247164</updated>
    </task>
    <task id="LOCAL-00027" summary="基本无bug">
      <option name="closed" value="true" />
      <created>1753262063290</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1753262063290</updated>
    </task>
    <task id="LOCAL-00028" summary="gzb本地化运行正常版本">
      <option name="closed" value="true" />
      <created>1753262090485</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1753262090485</updated>
    </task>
    <task id="LOCAL-00029" summary="gzb本地化运行正常版本">
      <option name="closed" value="true" />
      <created>1753347537880</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1753347537881</updated>
    </task>
    <task id="LOCAL-00030" summary="打卡点缺失问题修复">
      <option name="closed" value="true" />
      <created>1753347555315</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1753347555315</updated>
    </task>
    <task id="LOCAL-00031" summary="大问题版本，勿回滚到这里">
      <option name="closed" value="true" />
      <created>1753523293969</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1753523293969</updated>
    </task>
    <task id="LOCAL-00032" summary="稳定版本">
      <option name="closed" value="true" />
      <created>1753546938192</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1753546938192</updated>
    </task>
    <task id="LOCAL-00033" summary="绕远路解决">
      <option name="closed" value="true" />
      <created>1753560853240</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1753560853240</updated>
    </task>
    <task id="LOCAL-00034" summary="绕远路问题解决后的，数据单位问题解决">
      <option name="closed" value="true" />
      <created>1753685852695</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1753685852695</updated>
    </task>
    <task id="LOCAL-00035" summary="权限bug修改前">
      <option name="closed" value="true" />
      <created>1753688947696</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1753688947696</updated>
    </task>
    <task id="LOCAL-00036" summary="权限bug修改完成（待前端对接）">
      <option name="closed" value="true" />
      <created>1753693982560</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1753693982560</updated>
    </task>
    <task id="LOCAL-00037" summary="用户管理模块bug修改完成">
      <option name="closed" value="true" />
      <created>1753695861975</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1753695861975</updated>
    </task>
    <task id="LOCAL-00038" summary="完成商铺导入表格修改（仍存在所在大区的bug）">
      <option name="closed" value="true" />
      <created>1753728986996</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1753728986996</updated>
    </task>
    <task id="LOCAL-00039" summary="完成商铺导入表格修改（仍存在所在大区的bug）">
      <option name="closed" value="true" />
      <created>1753728995429</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1753728995429</updated>
    </task>
    <task id="LOCAL-00040" summary="完成修复大部分bug，车辆功能修改前">
      <option name="closed" value="true" />
      <created>1753899208018</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1753899208018</updated>
    </task>
    <task id="LOCAL-00041" summary="正常使用版本">
      <option name="closed" value="true" />
      <created>1754045919924</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1754045919924</updated>
    </task>
    <task id="LOCAL-00042" summary="聚集区bug修改前">
      <option name="closed" value="true" />
      <created>1754060719103</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1754060719103</updated>
    </task>
    <task id="LOCAL-00043" summary="聚集区bug修改前">
      <option name="closed" value="true" />
      <created>1754060774893</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1754060774893</updated>
    </task>
    <task id="LOCAL-00044" summary="聚集区打卡点修复完成">
      <option name="closed" value="true" />
      <created>1754067872652</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1754067872652</updated>
    </task>
    <task id="LOCAL-00045" summary="聚集区打卡点修复完成">
      <option name="closed" value="true" />
      <created>1754067898805</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1754067898805</updated>
    </task>
    <task id="LOCAL-00046" summary="聚集区打卡点修复完成">
      <option name="closed" value="true" />
      <created>1754067919203</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1754067919203</updated>
    </task>
    <task id="LOCAL-00047" summary="聚集区打卡点修复完成（真）">
      <option name="closed" value="true" />
      <created>1754068487547</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1754068487548</updated>
    </task>
    <option name="localTasksCounter" value="48" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="60c2f0be-6321-4abe-8677-f65d3e473a68" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="60c2f0be-6321-4abe-8677-f65d3e473a68">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/Desktop/烟草/交接信息/源代码/ycwl-ms-v3.0/cluster-calculate" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="实现凸包不会冲突，但是，工作时长还是不够均衡" />
    <MESSAGE value="中转站一能实现挺好的班组均衡，但有一点点凸包冲突，整体上不错，但是中转站2效果很差" />
    <MESSAGE value="引入mysqlBaits多数据源前进行备份" />
    <MESSAGE value="通过mybatisPlus实现多数据源切换，通过aop自定义注解读取本地文件源实现" />
    <MESSAGE value="完成另存为新版本功能" />
    <MESSAGE value="完善了导出路径的商铺增加修改状态" />
    <MESSAGE value="完成了另存为新版本，已经一些bug修复，部署了聚集区模块的多数据库，pickup模块，data模块的多数据库" />
    <MESSAGE value="新增版本间的差异检测" />
    <MESSAGE value="交接路径计算" />
    <MESSAGE value="基本无bug" />
    <MESSAGE value="gzb本地化运行正常版本" />
    <MESSAGE value="打卡点缺失问题修复" />
    <MESSAGE value="大问题版本，勿回滚到这里" />
    <MESSAGE value="稳定版本" />
    <MESSAGE value="绕远路解决" />
    <MESSAGE value="绕远路问题解决后的，数据单位问题解决" />
    <MESSAGE value="权限bug修改前" />
    <MESSAGE value="权限bug修改完成（待前端对接）" />
    <MESSAGE value="用户管理模块bug修改完成" />
    <MESSAGE value="完成商铺导入表格修改（仍存在所在大区的bug）" />
    <MESSAGE value="完成修复大部分bug，车辆功能修改前" />
    <MESSAGE value="正常使用版本" />
    <MESSAGE value="聚集区bug修改前" />
    <MESSAGE value="聚集区打卡点修复完成" />
    <MESSAGE value="聚集区打卡点修复完成（真）" />
    <option name="LAST_COMMIT_MESSAGE" value="聚集区打卡点修复完成（真）" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/path-calculate/src/test/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImplTest.java</url>
          <line>98</line>
          <option name="timeStamp" value="201" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/path-calculate/src/test/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImplTest.java</url>
          <line>107</line>
          <option name="timeStamp" value="219" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/guestbook/src/main/java/com/ict/ycwl/guestbook/service/impl/FeedbackServiceImpl.java</url>
          <line>77</line>
          <option name="timeStamp" value="301" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/guestbook/src/main/java/com/ict/ycwl/guestbook/service/impl/FeedbackServiceImpl.java</url>
          <line>84</line>
          <option name="timeStamp" value="312" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/guestbook/src/main/java/com/ict/ycwl/guestbook/service/impl/FeedbackServiceImpl.java</url>
          <line>78</line>
          <option name="timeStamp" value="313" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/guestbook/src/main/java/com/ict/ycwl/guestbook/service/impl/FeedbackServiceImpl.java</url>
          <line>79</line>
          <option name="timeStamp" value="314" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/guestbook/src/main/java/com/ict/ycwl/guestbook/service/impl/FeedbackServiceImpl.java</url>
          <line>80</line>
          <option name="timeStamp" value="315" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/guestbook/src/main/java/com/ict/ycwl/guestbook/service/impl/FeedbackServiceImpl.java</url>
          <line>81</line>
          <option name="timeStamp" value="316" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/pickup/src/main/java/com/ict/ycwl/pickup/service/impl/PickupUserServiceImpl.java</url>
          <line>416</line>
          <option name="timeStamp" value="367" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/data-management/src/main/java/com/ict/datamanagement/service/impl/DeliveryServiceImpl.java</url>
          <line>299</line>
          <option name="timeStamp" value="383" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.java</url>
          <line>1424</line>
          <option name="timeStamp" value="386" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.java</url>
          <line>1428</line>
          <option name="timeStamp" value="390" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.java</url>
          <line>1429</line>
          <option name="timeStamp" value="391" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.java</url>
          <line>1432</line>
          <option name="timeStamp" value="392" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.java</url>
          <line>1435</line>
          <option name="timeStamp" value="393" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.java</url>
          <line>1443</line>
          <option name="timeStamp" value="394" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/cluster-calculate/src/main/java/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.java</url>
          <line>1453</line>
          <option name="timeStamp" value="395" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/data-management/src/main/java/com/ict/datamanagement/service/impl/DeliveryServiceImpl.java</url>
          <line>420</line>
          <option name="timeStamp" value="396" />
        </line-breakpoint>
        <breakpoint enabled="true" type="java-exception">
          <properties class="java.lang.RuntimeException" package="java.lang" />
          <option name="timeStamp" value="397" />
        </breakpoint>
        <breakpoint enabled="true" type="java-exception">
          <properties class="java.lang.IllegalArgumentException" package="java.lang" />
          <option name="timeStamp" value="398" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="java.lang.String" memberName="hash" />
        <PinnedItemInfo parentTag="org.springframework.dao.CannotAcquireLockException" memberName="detailMessage" />
        <PinnedItemInfo parentTag="com.ict.datamanagement.domain.entity.Team" memberName="deliveryAreaName" />
        <PinnedItemInfo parentTag="org.apache.commons.math3.ml.clustering.CentroidCluster" memberName="points" />
      </pinned-members>
    </pin-to-top-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>