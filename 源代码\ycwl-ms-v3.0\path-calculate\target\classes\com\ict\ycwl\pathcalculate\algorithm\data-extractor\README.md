# 路径规划算法数据提取工具

## 🎯 功能概述

本工具用于从MySQL数据库中提取路径规划算法所需的测试数据，并生成标准化的JSON文件。**重点解决了数据关系一致性问题**，确保提取的数据在数量受限时仍能保持完整的层级关系。

## 🔧 核心特性

### ✅ 关系一致性保证
- **层级化提取**：按照 `班组 → 中转站 → 聚集区 → 时间矩阵` 的顺序提取数据
- **关系验证**：确保所有聚集区都能找到对应的中转站，中转站都能找到对应的班组
- **智能分配**：在数量受限时，平均分配各层级的数据，避免关系孤立
- **完整性检查**：自动验证数据完整性并生成详细报告

### 📊 数据规模支持
- **small**: 50个聚集区，5个中转站，3个班组
- **medium**: 200个聚集区，10个中转站，6个班组  
- **large**: 500个聚集区，20个中转站，10个班组
- **full**: 不限制数量，提取全量数据

### 🌟 质量控制
- **坐标边界验证**：限制在合理的地理范围内
- **数据过滤**：排除软删除和无效数据
- **时间矩阵补全**：自动生成缺失的点对点时间数据
- **错误处理**：详细的日志记录和异常处理

## 📂 文件结构

```
data-extractor/
├── config.py           # 数据库连接和提取配置
├── extract_data.py     # 主要提取脚本  
├── quick_start.py      # 快速启动脚本
├── requirements.txt    # Python依赖
└── README.md          # 本文档
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装Python依赖
pip install -r requirements.txt

# 或使用快速启动脚本（推荐）
python quick_start.py
```

### 2. 配置数据库
编辑 `config.py` 文件，修改数据库连接信息：
```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'your_actual_password',  # 修改为实际密码
    'database': 'ycdb',
    'charset': 'utf8mb4'
}
```

### 3. 运行数据提取
```bash
# 小规模测试数据
python extract_data.py --scale small --version v1.0

# 中等规模数据  
python extract_data.py --scale medium --version v1.1

# 全量数据
python extract_data.py --scale full --version latest
```

## 📋 提取流程详解

### 数据提取顺序
```
第1步：提取班组数据（顶层）
  └── 确保班组数据完整，作为层级根节点

第2步：提取中转站数据（基于班组关系）
  └── 只提取属于已提取班组的中转站
  └── 平均分配给各班组，确保负载均衡

第3步：更新班组关系数据
  └── 填充班组的中转站ID列表

第4步：提取聚集区数据（基于中转站关系）
  └── 只提取属于已提取中转站的聚集区
  └── 平均分配给各中转站

第5步：提取时间矩阵数据（基于所有坐标点）
  └── 只提取包含已有坐标点的时间数据
  └── 确保时间矩阵的相关性

第6步：生成缺失的时间矩阵
  └── 使用Haversine公式计算缺失的点对点时间

第7步：验证数据完整性
  └── 检查关系一致性和覆盖率

第8步：保存JSON文件
  └── 生成标准化的JSON文件和数据摘要
```

### 关系一致性保证

#### 问题描述
传统的按数量限制提取数据会导致：
- 聚集区关联的中转站ID在提取的中转站列表中不存在
- 中转站关联的班组ID在提取的班组列表中不存在
- 时间矩阵缺少关键的点对点数据

#### 解决方案
1. **自顶向下提取**：从班组开始，逐层向下提取
2. **关系过滤**：每层只提取与上层有关系的数据
3. **智能分配**：在数量限制下平均分配各父节点的子数据
4. **实时验证**：提取过程中实时检查关系完整性

## 📊 输出文件说明

### JSON文件结构
```
../data/v1.0/
├── accumulations.json      # 聚集区数据
├── transit_depots.json     # 中转站数据  
├── teams.json             # 班组数据
├── time_matrix.json       # 时间矩阵数据
└── data_summary.json      # 数据摘要统计
```

### 数据示例
```json
// accumulations.json
{
  "description": "聚集区数据，包含坐标、配送时间等信息",
  "version": "1.0.0",
  "scale": "small",
  "data": [
    {
      "accumulationId": 1,
      "accumulationName": "聚集区1", 
      "longitude": 113.596766,
      "latitude": 24.810403,
      "transitDepotId": 2,
      "deliveryTime": 15.0
    }
  ]
}
```

## 🔍 数据验证

### 完整性检查项目
- ✅ 聚集区与中转站关系完整性
- ✅ 中转站与班组关系完整性  
- ✅ 时间矩阵覆盖率（≥80%）
- ✅ 坐标有效性验证
- ✅ 数据质量统计

### 验证日志示例
```
INFO - 开始验证数据完整性...
INFO - 班组 1 分配中转站: 2 个
INFO - 班组 2 分配中转站: 3 个  
INFO - 中转站 2 分配聚集区: 8 个
INFO - 中转站 3 分配聚集区: 12 个
INFO - 时间矩阵覆盖率: 85.6% (234/273)
INFO - 数据完整性验证通过
```

## ⚙️ 配置选项

### 数据规模配置
```python
'data_scales': {
    'small': {
        'max_accumulations': 50,    # 最大聚集区数量
        'max_transit_depots': 5,    # 最大中转站数量  
        'max_teams': 3,             # 最大班组数量
    }
}
```

### 质量过滤配置
```python
'quality_filters': {
    'coordinate_bounds': {
        'longitude_min': 112.0,     # 经度范围
        'longitude_max': 116.0,
        'latitude_min': 23.0,       # 纬度范围
        'latitude_max': 26.0
    },
    'default_delivery_time': 15.0,  # 默认配送时间（分钟）
    'default_route_count': 3        # 默认路线数量
}
```

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   ```
   检查：数据库服务状态、连接信息、用户权限
   ```

2. **关系验证失败**
   ```
   可能原因：数据库中存在孤立的关系数据
   解决：检查数据库数据完整性，清理无效关系
   ```

3. **时间矩阵覆盖率低**
   ```
   原因：travel_time表数据不完整
   解决：系统会自动生成缺失数据，或检查原始数据质量
   ```

4. **坐标点过多导致SQL过长**
   ```
   解决：自动分批查询，无需手动处理
   ```

### 日志文件
- 详细日志：`data_extraction.log`
- 控制台输出：实时状态和错误信息

## 🔄 版本管理

### 版本命名规范
- `v1.0`, `v1.1` - 主版本.次版本
- `latest` - 自动使用当前日期
- `test` - 测试版本

### 版本目录结构
```
../data/
├── v1.0/          # 第一版数据
├── v1.1/          # 第二版数据  
└── latest/        # 最新版本
```

## 🤝 开发建议

### 添加新的数据源
1. 在 `config.py` 中添加SQL查询
2. 在 `DataExtractor` 中添加提取方法
3. 更新 `run_extraction()` 流程
4. 添加相应的验证逻辑

### 性能优化
- 大数据量时考虑分批查询
- 使用数据库索引优化查询性能
- 缓存重复的计算结果

### 数据质量改进
- 添加更多的数据验证规则
- 实现数据清洗功能
- 支持数据异常检测和修复

## 📞 技术支持

如遇问题，请检查：
1. 日志文件中的详细错误信息
2. 数据库连接和权限设置
3. 配置文件的正确性
4. Python依赖的完整安装

---

**重要提醒**：本工具的核心价值在于解决数据关系一致性问题，确保在任何数据规模下都能提取到可用的、关系完整的测试数据。 