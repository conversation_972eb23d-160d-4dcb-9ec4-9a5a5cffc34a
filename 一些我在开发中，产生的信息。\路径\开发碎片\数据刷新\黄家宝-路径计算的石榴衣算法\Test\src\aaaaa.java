import org.locationtech.jts.geom.Geometry;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class aaaaa {
    public static void main(String[] args) {
        Map<Integer, List<String>> res = new HashMap<>();
        List<String> a = new ArrayList<>();
        a.add("123");
        res.put(1, a);
        List<String> b = res.get(0);
        System.out.println(b);
    }
}
