-- 测试特殊商铺微调功能修复效果的SQL脚本
-- 数据库: ycdb
-- 用途: 验证is_special字段更新是否正确

-- 1. 查看当前特殊商铺状态
SELECT 
    customer_code,
    store_name,
    is_special,
    remark,
    special_type,
    accumulation_id,
    route_id,
    longitude,
    latitude
FROM store 
WHERE is_special = '1' 
  AND is_delete = 0
ORDER BY customer_code;

-- 2. 查看错误点记录
SELECT 
    error_point_id,
    current_store_longitude,
    current_store_latitude,
    pairing_store_longitude,
    pairing_store_latitude,
    is_delete
FROM error_point 
WHERE is_delete = 0;

-- 3. 模拟测试数据（如果需要）
-- 插入一个测试特殊商铺
/*
INSERT INTO store (
    customer_code, store_name, store_address, longitude, latitude,
    is_special, remark, special_type, accumulation_id, route_id,
    is_delete, create_time, update_time
) VALUES (
    'TEST001', '测试特殊商铺', '测试地址', 113.123456, 24.123456,
    '1', '测试备注', '测试类型', 1, 1,
    0, NOW(), NOW()
);

-- 插入对应的错误点记录
INSERT INTO error_point (
    current_store_longitude, current_store_latitude,
    pairing_store_longitude, pairing_store_latitude,
    is_delete, create_time, update_time
) VALUES (
    113.123456, 24.123456,
    113.654321, 24.654321,
    0, NOW(), NOW()
);
*/

-- 4. 验证修复后的状态查询
-- 执行微调后，检查商铺状态是否正确更新
SELECT 
    customer_code,
    store_name,
    is_special,
    remark,
    special_type,
    accumulation_id,
    route_id
FROM store 
WHERE customer_code = 'TEST001'  -- 替换为实际测试的客户编号
  AND is_delete = 0;

-- 5. 检查错误点是否被删除
SELECT COUNT(*) as remaining_error_points
FROM error_point 
WHERE (
    (current_store_longitude = 113.123456 AND current_store_latitude = 24.123456)
    OR 
    (pairing_store_longitude = 113.123456 AND pairing_store_latitude = 24.123456)
) AND is_delete = 0;

-- 6. 清理测试数据（如果需要）
/*
DELETE FROM store WHERE customer_code = 'TEST001';
DELETE FROM error_point WHERE current_store_longitude = 113.123456 AND current_store_latitude = 24.123456;
*/
