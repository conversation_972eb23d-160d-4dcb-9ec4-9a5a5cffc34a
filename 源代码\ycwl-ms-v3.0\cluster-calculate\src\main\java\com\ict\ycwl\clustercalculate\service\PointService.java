package com.ict.ycwl.clustercalculate.service;

import com.ict.ycwl.clustercalculate.pojo.Accumulation;
import com.ict.ycwl.clustercalculate.pojo.ErrorPointsByAccumulation;
import com.ict.ycwl.clustercalculate.pojo.ListErrorCluster;
import com.ict.ycwl.clustercalculate.pojo.ListErrorPointFather;
import com.ict.ycwl.clustercalculate.pojo.ListPoint;
import com.ict.ycwl.common.web.AjaxResult;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PointService {

    /**
     * 返回地图中所有数据点，每个数据点包含经度、纬度、状态和该商铺名字
     *
     * @return 返回所有数据点
     */
    Object getMapResultPoints();

    /**
     * 返回右侧栏中所有数据点，每个数据点包含所属聚集区名字、该商铺名字
     *
     * @return 返回所有数据点
     */
    List<ListPoint> getListResultPoints();

//    /**
//     * 检查当前大区是否有错误点
//     *
//     * @param areaName 当前大区名字
//     * @return 当前大区错误点对数
//     */
//    int checkErrorPoint(String areaName);

    /**
     * 检查所有大区是否有错误点
     *
     * @return 错误点对数
     */
    int checkErrorPoints();

    /**
     * 获取错误点列表
     *
     * @return 所有含有错误点的聚集区
     */
    List<ListErrorCluster> getErrorPoints1();

    List<ListErrorPointFather> getErrorPointsPlus();

    /**
     * 获取按聚集区分组的错误点数据
     *
     * @return 按聚集区分组的错误点列表
     */
    List<ErrorPointsByAccumulation> getErrorPointsByAccumulation();

    /**
     * 获取距离当前点最近的三个聚集区
     *
     * @param longitude 当前点经度
     * @param latitude  当前点纬度
     * @return 聚集区数组
     */
    List<Accumulation> getClosestPoints(double longitude, double latitude);

    /**
     * 商铺微调聚集区
     *
     * @param longitude      当前微调商铺的经度
     * @param latitude       当前微调商铺的纬度
     * @param accumulationId 要微调到的聚集区的id
     * @return 返回成功操作数
     */
    int updateStoreAccumulationId(double longitude, double latitude, Long accumulationId);

    AjaxResult perview();

    AjaxResult adjustment();


    AjaxResult getAllAcc();

}
