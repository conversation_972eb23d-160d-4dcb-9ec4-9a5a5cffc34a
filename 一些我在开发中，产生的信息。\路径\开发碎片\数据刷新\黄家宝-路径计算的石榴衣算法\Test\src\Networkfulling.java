import org.locationtech.jts.geom.*;
import org.locationtech.jts.geom.util.GeometryFixer;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.precision.GeometryPrecisionReducer;
import org.locationtech.jts.simplify.TopologyPreservingSimplifier;
import org.locationtech.jts.triangulate.DelaunayTriangulationBuilder;
import org.locationtech.jts.triangulate.VoronoiDiagramBuilder;
import org.locationtech.jts.triangulate.quadedge.QuadEdgeSubdivision;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.CoderResult;
import java.util.*;

public class Networkfulling {
    private static GeometryFactory geometryFactory = new GeometryFactory();

    public static void main(String[] args) throws IOException, ParseException {
        List<Polygon> polygons = new ArrayList<>();
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.727166, 25.304861),
                new Coordinate(113.733395, 25.25601),
                new Coordinate(113.865906, 25.201329),
                new Coordinate(113.937188, 25.303314),
                new Coordinate(113.955875, 25.330793),
                new Coordinate(113.836659, 25.831624),
                new Coordinate(113.727166, 25.304861)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.793132, 25.152474),
                new Coordinate(113.838499, 25.139183),
                new Coordinate(113.943643, 25.133792),
                new Coordinate(113.799577, 25.162533),
                new Coordinate(113.793132, 25.152474)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.774136, 24.915802),
                new Coordinate(113.900526, 24.960427),
                new Coordinate(113.814666, 24.976133),
                new Coordinate(113.774136, 24.915802)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.744233, 25.042364),
                new Coordinate(113.745712, 25.03193),
                new Coordinate(113.780645, 25.027158),
                new Coordinate(113.830642, 25.056242),
                new Coordinate(113.830316, 25.064812),
                new Coordinate(113.825693, 25.071896),
                new Coordinate(113.757961, 25.059644),
                new Coordinate(113.706815, 25.066009),
                new Coordinate(113.744233, 25.042364)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.658321, 25.080264),
                new Coordinate(113.717595, 25.088101),
                new Coordinate(113.68599, 25.091865),
                new Coordinate(113.658321, 25.080264)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.726711, 25.088577),
                new Coordinate(113.739468, 25.081346),
                new Coordinate(113.744636, 25.083087),
                new Coordinate(113.745773, 25.086166),
                new Coordinate(113.745322, 25.089879),
                new Coordinate(113.733613, 25.089755),
                new Coordinate(113.726711, 25.088577)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.746279, 25.088949),
                new Coordinate(113.754657, 25.074007),
                new Coordinate(113.765271, 25.101996),
                new Coordinate(113.746753, 25.094568),
                new Coordinate(113.746279, 25.088949)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.560588, 25.088858),
                new Coordinate(113.624317, 24.965148),
                new Coordinate(113.644577, 25.052965),
                new Coordinate(113.6549, 25.109814),
                new Coordinate(113.638226, 25.1129),
                new Coordinate(113.627673, 25.111031),
                new Coordinate(113.560588, 25.088858)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.438228, 25.222742),
                new Coordinate(113.599192, 25.254163),
                new Coordinate(113.598679, 25.258883),
                new Coordinate(113.438228, 25.222742)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(114.097266, 24.010266),
                new Coordinate(114.117483, 23.961694),
                new Coordinate(114.14478, 23.974555),
                new Coordinate(114.17876, 24.028903),
                new Coordinate(114.16795, 24.044339),
                new Coordinate(114.133139, 24.05045),
                new Coordinate(114.097266, 24.010266)
        }));

        List<Polygon> De_polygons = new ArrayList<>();
        De_polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.521913, 24.788187),
                new Coordinate(113.523381, 24.779982),
                new Coordinate(113.526114, 24.778843),
                new Coordinate(113.556485, 24.779326),
                new Coordinate(113.533929, 24.824828),
                new Coordinate(113.521913, 24.788187)
        }));
        De_polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.551172, 24.793437),
                new Coordinate(113.560325, 24.794646),
                new Coordinate(113.562777, 24.7982),
                new Coordinate(113.561867, 24.798907),
                new Coordinate(113.553801, 24.803802),
                new Coordinate(113.551172, 24.793437)
        }));

        System.out.println("\n\nStep: 打印结果\n");
        System.out.println(solve(polygons));
    }

    public static Geometry solve(List<Polygon> polygons) throws IOException {
        System.out.println("\n\nStep: 打印凸包");
        for (Polygon p : polygons) System.out.println(p);
        System.out.println("\n\n");
        Geometry triangleNetwork = buildTriangulatedNetwork(polygons);
        triangleNetwork = merge_Network(triangleNetwork, polygons);
        return triangleNetwork;
    }

    public static List<Polygon> test(Map<Polygon, Integer> polygons_group, Polygon boundary) throws IOException {
        System.out.println("\n\nStep: 打印凸包");
        List<Polygon> polygons = new ArrayList<>(polygons_group.keySet());
        System.out.println("\n\n\n\n这是更新后的");
        for(Polygon polygon : polygons) System.out.println(polygon);
        return polygons;
    }

    public static Map<Integer, List<Geometry>> solve(Map<Polygon, Integer> polygons_group, Polygon boundary) throws IOException {
        System.out.println("\n\nStep: 打印凸包");
        List<Polygon> polygons = new ArrayList<>(polygons_group.keySet());
        for (Polygon p : polygons) System.out.println(p);
        System.out.println("\n\n");
        Geometry triangleNetwork = buildTriangulatedNetwork(polygons);
/*        for(int i = 0; i < triangleNetwork.getNumGeometries(); i++){
            System.out.println(triangleNetwork.getGeometryN(i));
        }*/
        List<Geometry> new_triangleNetwork = new ArrayList<>();
        for(int i = 0; i < triangleNetwork.getNumGeometries(); i++){
            Geometry geometry = triangleNetwork.getGeometryN(i);
            if(geometry.isEmpty()) continue;
            else new_triangleNetwork.add(geometry);
        }
        triangleNetwork = geometryFactory.createGeometryCollection(new_triangleNetwork.toArray(new Geometry[0]));
        Map<Integer, List<Geometry>> merge_polygons = new HashMap<>();
        merge_polygons = merge_Network(triangleNetwork, polygons_group, boundary);
        return merge_polygons;
    }

    // 进行网格合并_分组版
    private static Map<Integer, List<Geometry>> merge_Network(Geometry triangleNetwork, Map<Polygon, Integer> polygons_group, Polygon boundary) throws IOException {
        List<Polygon> polygons  = new ArrayList<>(polygons_group.keySet());
        List<Geometry> merge_net = new ArrayList<>(polygons);
        List<Geometry> triangles = new ArrayList<>();
        HashMap<Geometry, Integer> polygonBelong = new HashMap<>(polygons_group);
        System.out.println("开始合并网格");
        for(int i = 0; i < triangleNetwork.getNumGeometries(); i++){
            if(triangleNetwork.getGeometryN(i).isEmpty()) System.out.println("NOOOOOOOOO");
            triangles.add(triangleNetwork.getGeometryN(i));
        }
        List<Geometry> to_triangles = new ArrayList<>(triangles);
        List<Geometry> new_merge_net = new ArrayList<>();
        int cnt = 0;
        while(triangles.size() != 0 && cnt <= 100) {  // 最多进行一百轮合并
            //System.out.println("正在进行第"+cnt+"轮合并");
            for (Geometry p : merge_net) {
                Geometry polygon = p;

                for (int i = 0; i < triangles.size(); i++) {
                    //System.out.println("这里??");
                    Geometry triangle = triangles.get(i);
                    if (!p.intersection(triangle).isEmpty()) {
                        if(p.intersection(triangle) instanceof Point || p.intersection(triangle) instanceof MultiPoint){
                            continue;
                        }
                        polygon = polygon.union(triangle);
                        to_triangles.remove(triangle);
                    }
                }
                polygon = get_shell(polygon);
                polygonBelong.put(polygon, polygonBelong.get(p));
                new_merge_net.add(polygon);
                triangles = new ArrayList<>(to_triangles);
            }
            merge_net = new ArrayList<>(new_merge_net);
            new_merge_net.clear();
            cnt++;
            if(cnt == 15){
                System.out.println();
            }
            System.out.println("正在进行第"+cnt+"轮合并");
            /*BufferedWriter Writer = new BufferedWriter(new FileWriter("net_step: " + cnt + ".txt"));
            for (Geometry m : merge_net) {
                Writer.write(m.toText() + "\n");
            }
            Writer.write("\n\n\n\n")*/;
        }

        // 进行地图分割
        System.out.println("开始切割边界");
        new_merge_net = new ArrayList<>();
        for (Geometry p : merge_net) {
            Geometry polygon = p;
            if(!polygon.difference(boundary).isEmpty()){
                polygon = polygon.intersection(boundary);
                polygonBelong.put(polygon, polygonBelong.get(p));
            }
            new_merge_net.add(polygon);
        }
        merge_net = new ArrayList<>(new_merge_net);
        new_merge_net.clear();


        Map<Integer, List<Geometry>> net_group = new HashMap<>();
        for (Geometry geometry : merge_net) {
            if (polygonBelong.containsKey(geometry)) {
                Integer group = polygonBelong.get(geometry);
                net_group.computeIfAbsent(group, k -> new ArrayList<>()).add(geometry);
            }
        }
        Map<Integer, List<Geometry>> new_net_group = new HashMap<>();
        for (Map.Entry<Integer, List<Geometry>> entry : net_group.entrySet()) {
            Integer groupId = entry.getKey();
            List<Geometry> block = entry.getValue();
            block = sequential(block);
            new_net_group.put(groupId, block);
        }
        net_group = new HashMap<>(new_net_group);
        new_net_group.clear();
        return net_group;
    }

    // 进行网格合并
    private static Geometry merge_Network(Geometry triangleNetwork, List<Polygon> polygons) throws IOException {
        List<Geometry> merge_net = new ArrayList<>(polygons);
        List<Geometry> triangles = new ArrayList<>();
        System.out.println("开始合并网格");
        for(int i = 0; i < triangleNetwork.getNumGeometries(); i++){
            triangles.add(triangleNetwork.getGeometryN(i));
        }
        List<Geometry> to_triangles = new ArrayList<>(triangles);
        List<Geometry> new_merge_net = new ArrayList<>();
        int cnt = 0;
        while(triangles.size() != 0 && cnt <= 100) {  // 最多进行一百轮合并
            //System.out.println("正在进行第"+cnt+"轮合并");
            for (Geometry p : merge_net) {
                Geometry polygon = p;

                for (int i = 0; i < triangles.size(); i++) {
                    //System.out.println("这里??");
                    Geometry triangle = triangles.get(i);
                    if (!p.intersection(triangle).isEmpty()) {
                        if(p.intersection(triangle) instanceof Point || p.intersection(triangle) instanceof MultiPoint){
                            continue;
                        }
                        polygon = polygon.union(triangle);
                        to_triangles.remove(triangle);
                    }
                }
                polygon = get_shell(polygon);
                new_merge_net.add(polygon);
                triangles = new ArrayList<>(to_triangles);
            }
            merge_net = new ArrayList<>(new_merge_net);
            new_merge_net.clear();
            cnt++;
            System.out.println("正在进行第"+cnt+"轮合并");
            BufferedWriter Writer = new BufferedWriter(new FileWriter("net_step: " + cnt + ".txt"));
            for (Geometry m : merge_net) {
                Writer.write(m.toText() + "\n");
            }
            Writer.write("\n\n\n\n");
        }

        Geometry net = geometryFactory.createGeometryCollection(merge_net.toArray(new Geometry[0]));
        return net;
    }

    // 创建三角网格
    public static Geometry buildTriangulatedNetwork(List<Polygon> polygons) throws IOException {
        List<Coordinate> coordinates = new ArrayList<>();
        Geometry triangleNetwork = null;
        for (Polygon polygon : polygons) {
            for (Coordinate coord : polygon.getCoordinates()) {
                coordinates.add(coord);
            }
        }
        coordinates.add(new Coordinate(112.8, 23.8));
        coordinates.add(new Coordinate(112.8, 26.0));
        coordinates.add(new Coordinate(115.0, 23.8));
        coordinates.add(new Coordinate(115.0, 26.0));
        //(112.8, 115.0, 23.8, 26.0)

        DelaunayTriangulationBuilder builder = new DelaunayTriangulationBuilder();
        builder.setSites(coordinates);
        QuadEdgeSubdivision subdivision = builder.getSubdivision();

        triangleNetwork =  subdivision.getTriangles(geometryFactory);
        System.out.println("一");
        coordinates.clear();

        /*for (int i = 0; i < triangleNetwork.getNumGeometries(); i++) {
            Polygon polygon = (Polygon) triangleNetwork.getGeometryN(i);
            coordinates.add(polygon.getCentroid().getCoordinate());
            for (Coordinate coord : polygon.getCoordinates()) {
                coordinates.add(coord);
            }
        }

        VoronoiDiagramBuilder voronoiBuilder = new VoronoiDiagramBuilder();
        voronoiBuilder.setSites(coordinates);
        voronoiBuilder.setClipEnvelope(new Envelope(112.8, 115.0, 23.8, 26.0));
        triangleNetwork = voronoiBuilder.getDiagram(geometryFactory);
        coordinates.clear();*/



        for (int i = 0; i < triangleNetwork.getNumGeometries(); i++) {
            Polygon polygon = (Polygon) triangleNetwork.getGeometryN(i);
            coordinates.add(polygon.getCentroid().getCoordinate());
            for (Coordinate coord : polygon.getCoordinates()) {
                coordinates.add(coord);
            }
        }
        builder = new DelaunayTriangulationBuilder();
        builder.setSites(coordinates);
        subdivision = builder.getSubdivision();
        triangleNetwork =  subdivision.getTriangles(geometryFactory);
        System.out.println("二");

        coordinates.clear();
        for (int i = 0; i < triangleNetwork.getNumGeometries(); i++) {
            Polygon polygon = (Polygon) triangleNetwork.getGeometryN(i);
            coordinates.add(polygon.getCentroid().getCoordinate());
            for (Coordinate coord : polygon.getCoordinates()) {
                coordinates.add(coord);
            }
        }

        builder = new DelaunayTriangulationBuilder();
        builder.setSites(coordinates);
        subdivision = builder.getSubdivision();
        triangleNetwork =  subdivision.getTriangles(geometryFactory);
        System.out.println("三");

        /*coordinates.clear();
        for (int i = 0; i < triangleNetwork.getNumGeometries(); i++) {
            Polygon polygon = (Polygon) triangleNetwork.getGeometryN(i);
            coordinates.add(polygon.getCentroid().getCoordinate());
            for (Coordinate coord : polygon.getCoordinates()) {
                coordinates.add(coord);
            }
        }
        builder = new DelaunayTriangulationBuilder();
        builder.setSites(coordinates);
        subdivision = builder.getSubdivision();
        triangleNetwork =  subdivision.getTriangles(geometryFactory);
        System.out.println("四");*/

        List<Geometry> fix = new ArrayList<>();
        for(int i = 0; i < triangleNetwork.getNumGeometries(); i++){
            fix.add(makeValid(triangleNetwork.getGeometryN(i)));
        }
        fix = check_crash(fix, polygons);
        /*BufferedWriter Writer = new BufferedWriter(new FileWriter("original_net.txt"));
        for (Geometry m : fix) {
            Writer.write(m.toText() + "\n");
        }
        Writer.write("\n\n\n\n");*/
        return geometryFactory.createGeometryCollection(fix.toArray(new Geometry[0]));
    }

    // 检查网格冲突
    private static List<Geometry> check_crash(List<Geometry> fix, List<Polygon> polygons) {
        List<Geometry> no_crash = new ArrayList<>();
        HashMap<Geometry, Polygon> is_crash = new HashMap<>();
        HashMap<Geometry, Boolean> is_check = new HashMap<>();
        for(Geometry triangle : fix){
            is_crash.put(triangle, null);
            is_check.put(triangle, false);
        }
        System.out.print("Has crash: ");
        int cnt = 0;
        for(Geometry triangle : fix){
            for(Polygon polygon: polygons){
                if(!triangle.intersection(polygon).isEmpty() && triangle.intersection(polygon) instanceof Polygon){
                    if(is_crash.get(triangle) == null) {
                        is_crash.put(triangle, polygon);
                        //is_check.put(triangle, true);
                    } else{
                        if(is_check.get(triangle)) continue;
                        is_check.put(triangle, true);
                        cnt++;
                        System.out.print(" " + cnt + "\n");
                        Geometry inner = triangle.intersection(polygon);
                        Geometry other_inner = triangle.difference(polygon);
                        System.out.println(inner);
                        System.out.println(other_inner + "\n");
                        no_crash.add(inner);
                        no_crash.add(other_inner);
                    }
                }
            }
        }
        for(Geometry triangle : fix){
            if(!is_check.get(triangle)){
                no_crash.add(triangle);
            }
        }
        System.out.println();
        System.out.println("冲突检测完毕");
        return no_crash;
    }

    // 降低网格精度
    public static Geometry makeValid(Geometry geometry) {
        Geometry fixedGeometry = GeometryFixer.fix(geometry);

        // 简化几何体
        geometry = TopologyPreservingSimplifier.simplify(fixedGeometry, 0.0000001);
        // 检查几何图形是否有效
        if (!geometry.isValid()) {
            System.out.println("Invalid geometry, applying buffer(0) to fix...");
            geometry = geometry.buffer(0);
        }

        // 使用 GeometryPrecisionReducer 来降低几何图形的精度
        PrecisionModel precisionModel = new PrecisionModel(1e10);
        GeometryPrecisionReducer reducer = new GeometryPrecisionReducer(precisionModel);
        geometry = reducer.reduce(geometry);

        return geometry;
    }

    // 获取外轮廓多边形
    public static Geometry get_shell(Geometry updatedPolygon){
        //updatedPolygon = makeValid(updatedPolygon);
        if(!updatedPolygon.getInteriorPoint().isEmpty()){
            // 将updatedPolygon赋值为updatedPolygon与它的内轮廓形成的多边形的union
            GeometryFactory geometryFactory = new GeometryFactory();
            //System.out.println(updatedPolygon);
            for (int j = 0; j < ((Polygon) updatedPolygon).getNumInteriorRing(); j++) {
                LinearRing interiorRing = ((Polygon) updatedPolygon).getInteriorRingN(j);
                Polygon interiorPolygon = geometryFactory.createPolygon(interiorRing, null);
                updatedPolygon = updatedPolygon.union(interiorPolygon);
            }
        }
        return updatedPolygon;
    }

    // 排序多边形
    public static List<Geometry> sequential(List<Geometry> polygons) {
        HashMap<Coordinate, Geometry> location = new HashMap<>();
        List<Coordinate> coordinates = new ArrayList<>();
        List<Geometry> sequential_polygon = new ArrayList<>();
        for (Geometry polygon : polygons) {
            Coordinate coordinate = polygon.getCentroid().getCoordinate();
            location.put(coordinate, polygon);
            coordinates.add(coordinate);
        }
        coordinates.sort(coordinateComparator);
        for(Coordinate coordinate : coordinates){
            sequential_polygon.add(location.get(coordinate));
        }
        return sequential_polygon;
    }

    // 对Coordinate的排序函数
    static Comparator<Coordinate> coordinateComparator = (c1, c2) ->{
        if(c1.y != c2.y){
            return Double.compare(c2.y, c1.y);
        } else{
            return Double.compare(c1.x, c2.x);

        }
    };


}
