import org.locationtech.jts.geom.Polygon;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class check_overlap {

    public static List<Polygon> checkOverlap(List<Polygon> polygons) {
        // Map 用于跟踪检查过的多边形
        Map<Polygon, Boolean> checkedMap = new HashMap<>();

        // 存放重叠的多边形
        Map<Polygon, Boolean> overlappingPolygons = new HashMap<>();

        // 外层循环遍历每个多边形
        for (int i = 0; i < polygons.size(); i++) {
            Polygon polygonA = polygons.get(i);

            // 检查是否已经检查过
            if (checkedMap.containsKey(polygonA)) {
                continue;
            }

            // 内层循环与后续的多边形进行比较
            for (int j = i + 1; j < polygons.size(); j++) {
                Polygon polygonB = polygons.get(j);

                // 检查是否已经检查过
                if (checkedMap.containsKey(polygonB)) {
                    continue;
                }

                // 检查多边形是否重叠
                if (!polygonA.intersection(polygonB).isEmpty()) {
                    // 如果有重叠，将二者都添加到重叠地图中
                    overlappingPolygons.put(polygonA, true);
                    overlappingPolygons.put(polygonB, true);
                }
            }

            // 将当前多边形标记为已检查
            checkedMap.put(polygonA, true);
        }

        // 打印所有重叠的多边形
        if (!overlappingPolygons.isEmpty()) {
            System.out.println("重叠的多边形:");
            for (Polygon poly : overlappingPolygons.keySet()) {
                System.out.println(poly);
            }
        } else {
            System.out.println("没有重叠的多边形.");
        }

        polygons.removeAll(overlappingPolygons.keySet());

        // 打印剩余的多边形
        if (!polygons.isEmpty()) {
            System.out.println("剩余的多边形:");
            for (Polygon poly : polygons) {
                System.out.println(poly);
            }
        } else {
            System.out.println("所有多边形均已删除.");
        }

        // 返回剩余的多边形列表
        return polygons;
    }


}
