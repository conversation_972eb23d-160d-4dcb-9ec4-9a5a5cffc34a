import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.jts.geom.PrecisionModel;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryCollection;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import java.util.ArrayList;
import java.util.List;

public class Test {

    private GeometryFactory geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);

    public static Map<Polygon, Integer> To_group(List<Polygon> route) {
        Map<Polygon, Integer> group = new HashMap<>();
        int cnt = 0, idx = 1;
        for(Polygon polygon : route){
            cnt++;
            if(cnt % 30 == 0) idx++;
            group.put(polygon, idx);
        }
        return group;
    }

    public static void main(String[] args) {
    }
}
