<template>
  <div class="AreaAdjust">
    <div class="back">
      <el-icon size="25" class="backBtn" @click="backBtn">
        <ArrowLeftBold /> </el-icon
      >&nbsp;聚集区微调&nbsp;<el-icon size="25">
        <LocationInformation />
      </el-icon>
    </div>
    <div
      id="container"
      v-loading="isMaoFinished"
      element-loading-text="地图数据加载中..."
      element-loading-background="rgba(0,23,49,0.8)"
    ></div>

    <BorderBox12
      backgroundColor="#001731"
      v-loading="isFinished"
      element-loading-text="加载中..."
      element-loading-background="rgba(0,23,49,0.8)"
    >
      <el-empty class="empty" v-if="isShow" description="暂无数据" />
      <el-scrollbar height="75vh">
        <div class="adjustCollapse">
          <div class="title">特殊点详细：</div>
          <el-collapse v-model="activeNames1" accordion>
            <el-collapse-item
              v-for="item in clusterStore.errorDepotAll"
              :key="item.name"
              :title="item.name"
              :name="item.name"
            >
              <ul>
                <li
                  v-for="(son, idx) in item.son"
                  :key="idx"
                  :id="'shop-' + son.longitude + '-' + son.latitude"
                  :class="
                    longitude == son.longitude && latitude == son.latitude
                      ? 'active'
                      : ''
                  "
                  @click="openAdjustDialog(item.name, item)"
                >
                  {{ son.name }}
                </li>
              </ul>
            </el-collapse-item>
          </el-collapse>
        </div>
      </el-scrollbar>
      <div class="flex">
        <el-button type="primary" @click="togglePreview">{{
          isPreviewing ? "取消预览" : "调整预览"
        }}</el-button>
        <el-button type="primary" @click="oneKeyAdjust">一键调整</el-button>
      </div>
    </BorderBox12>
    <el-dialog
      style="transform: translate(36.5vw, 0)"
      v-model="isOpenAdjustDialog"
      width="25%"
      :modal="false"
      :before-close="closeAdjustDialog"
      :append-to-body="true"
    >
      <div class="adjustDialogContent">
        <div class="adjustDialogChange">
          <div style="font-size: 2.2vh; font-weight: bold; margin-bottom: 1vh">
            商铺所属聚集区调整：把该商铺调整到正确聚集区以内
          </div>
          <!-- <div style="margin-bottom: 2vh">把该商铺调整到正确聚集区以内</div> -->
          <div style="display: flex; justify-content: center">
            <el-select
              v-model="rightSelect"
              value-key="accumulationAddress"
              style="width: 9vw"
            >
              <el-option
                v-for="item in clusterStore.AccumlationInfo"
                :key="item.accumulationAddress"
                :label="item.accumulationAddress"
                :value="item"
              />
            </el-select>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeAdjustDialog">取消</el-button>
          <el-button @click="adjustConfirmChange">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { MAP_KEY, SECURITY_CODE } from "@/utils/getMapKey";
  import { BorderBox12 } from "@dataview/datav-vue3";
  import { ArrowLeftBold, LocationInformation } from "@element-plus/icons-vue";
  import { useRouter } from "vue-router";
  import { useClusterStore } from "@/store/cluster";
  import {
    IAccumulationIdInfo,
    IErrorPoints_data,
    IShopData,
  } from "@/types/cluster";
  import { mapIcon } from "@/utils/mapBluePoint";
  //@ts-ignore
  import AMapLoader from "@amap/amap-jsapi-loader";
  window._AMapSecurityConfig = {
    securityJsCode: SECURITY_CODE,
  };
  const router = useRouter();
  function backBtn() {
    router.replace("/home/<USER>/area");
  }
  //聚集区Store
  const clusterStore = useClusterStore();
  //定义是否加载完成的变量
  const isFinished = ref<boolean>(true);
  //定义变量是否展示
  const isShow = ref<boolean>(false);
  const activeNames1 = ref<string[]>([]);
  const rightSelect = ref<any>({
    accumulationAddress: "",
  });
  //弹窗里面的数据定义
  const infoData = ref<IErrorPoints_data>();
  const infoData_fatherName = ref<string>();
  //定义是否打开弹窗的变量
  const isOpenAdjustDialog = ref(false);
  // 页面挂载时先请求数据，数据到后再初始化地图
  onMounted(async () => {
    isMaoFinished.value = true;
    try {
      // 同时请求两个接口
      const [checkResult, depotResult] = await Promise.all([
        clusterStore.getCheckErrorPointsAction(),
        clusterStore.getErrorDepotAllAction(),
      ]);



      // 检查是否有特殊点
      if (clusterStore.ErrorPoints == 0) {
        isShow.value = true;
        ElMessage({
          message: "当前没有特殊点",
          type: "error",
        });
      }

      isFinished.value = false;
      isMaoFinished.value = false;

      // 数据到后再初始化地图
      AMapLoader.load({
        key: MAP_KEY,
        version: "2.0",
        plugins: ["AMap.DistrictSearch"],
      })
        .then((AMap: any) => {
          const district = new AMap.DistrictSearch({
            subdistrict: 1,
            extensions: "all",
            level: "province",
          });
          district.search("韶关市", function (_: any, result: any) {
            const bounds = result.districtList[0].boundaries;
            const mask = [];
            for (let i = 0; i < bounds.length; i++) {
              mask.push([bounds[i]]);
            }
            map = new AMap.Map("container", {
              mask: mask,
              zoom: 9,
              expandZoomRange: true,
              zooms: [9, 20],
              center: [113.767587, 24.718014],
              viewMode: "3D",
              zoomEnable: true,
              resizeEnable: true,
            });
            for (let i = 0; i < bounds.length; i++) {
              const polyline = new AMap.Polyline({
                path: bounds[i],
                strokeColor: "#3078AC",
                strokeWeight: 2,
              });
              polyline.setMap(map);
            }
            // 只渲染 errorDepotAll
            renderErrorDepotMarkers();
          });
        })
        .catch((e: Error) => {
          console.log(e);
          isMaoFinished.value = false;
        });
    } catch (error) {
      console.error("接口请求失败:", error);
      isMaoFinished.value = false;
      ElMessage.error("数据加载失败");
    }
  });

  //定义变量需要修改的商铺的经纬度
  const optionItemData = ref<IShopData>({
    longitude: 0,
    latitude: 0,
  });
  //打开弹窗
  const openAdjustDialog = async (fatherName: string, info: any) => {
    console.log("🎯 点击微调 - 父商铺:", fatherName);
    console.log("🎯 点击微调 - 传入数据:", info);
    console.log("🎯 点击微调 - customerCode:", info.customerCode);

    isOpenAdjustDialog.value = true;
    infoData.value = info;
    infoData_fatherName.value = fatherName;

    optionItemData.value = {
      longitude: infoData.value.longitude,
      latitude: infoData.value.latitude,
      customerCode: infoData.value.customerCode,
    };



    longitude.value = infoData.value.longitude;
    latitude.value = infoData.value.latitude;



    map.setZoomAndCenter(18, [longitude.value, latitude.value]);
    setTimeout(() => {
      const el = document.getElementById(
        "shop-" + infoData.value.longitude + "-" + infoData.value.latitude
      );
      if (el) {
        el.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    }, 300);

    await clusterStore.getClosestPointsAction(optionItemData.value);
  };

  const closeAdjustDialog = () => {
    isOpenAdjustDialog.value = false;
    //选择器数据清空
    rightSelect.value = {
      accumulationAddress: "",
    };
  };
  // 地图右边栏互动
  const longitude = ref<number>();
  const latitude = ref<number>();
  //确定聚集区微调
  const adjustConfirmChange = () => {


    const AccumulationIdInfo = ref<IAccumulationIdInfo>({
      longitude: 0,
      latitude: 0,
      accumulationId: "",
    });
    AccumulationIdInfo.value = {
      accumulationId: rightSelect.value.accumulationId!,
      latitude: optionItemData.value.latitude,
      longitude: optionItemData.value.longitude,
      customerCode: infoData.value?.customerCode, // 添加customerCode
    };

    console.log("🔍 微调参数检查:", {
      customerCode: infoData.value?.customerCode,
      longitude: optionItemData.value.longitude,
      latitude: optionItemData.value.latitude,
      accumulationId: rightSelect.value.accumulationId,
      infoData: infoData.value
    });



    clusterStore
      .postUpdateStoreAccumulationIdAction(AccumulationIdInfo.value)
      .then(() => {
        console.log("微调请求完成，状态码:", clusterStore.UpdateStoreAccumulationIdCode);
        setTimeout(() => {
          if (clusterStore.UpdateStoreAccumulationIdCode == 200) {
            console.log("微调成功，准备刷新页面");
            location.reload();
          }
        }, 1500);
      });
  };

  const isMaoFinished = ref<boolean>(true);
  let map: any = null;
  let allMarkers: any[] = [];
  let allPolylines: any[] = [];

  // 地图渲染逻辑重写，使用 errorDepotAll
  function renderErrorDepotMarkers() {
    // 清除原有marker
    if (allMarkers && map) {
      allMarkers.forEach((marker) => map.remove(marker));
      allMarkers = [];
      console.log(clusterStore.errorDepotAll);
    }
    if (!clusterStore.errorDepotAll) {
      ElMessage.error("地图数据为空，请先在聚集区计算页面加载地图数据");
      return;
    }
    clusterStore.errorDepotAll.forEach((item) => {
      // item主点marker
      const itemMarker = new AMap.Marker({
        position: new AMap.LngLat(item.longitude, item.latitude),
        offset: new AMap.Pixel(-16, -16),
        icon: new AMap.Icon({
          size: new AMap.Size(32, 32),
          image: mapIcon.red,
          imageSize: new AMap.Size(32, 32),
        }),
        zIndex: 200,
        title: item.name,
      });
      // 浮窗内容
      const infoHtml = `
      <div class="info-card">
        <h3>${item.name}</h3>
        <p>备注：${item.remark || ""}</p>
        <p>类型：${item.specialType || ""}</p>
      </div>
    `;
      const infoWindow = new AMap.InfoWindow({
        content: infoHtml,
        offset: new AMap.Pixel(0, -30),
        isCustom: true,
      });
      itemMarker.on("mouseover", () => {
        infoWindow.open(map, itemMarker.getPosition());
      });
      itemMarker.on("mouseout", () => {
        infoWindow.close();
      });
      itemMarker.on("click", function () {
        activeNames1.value = [item.name];
        longitude.value = item.longitude;
        latitude.value = item.latitude;
        document.getElementById(item.name)?.scrollIntoView();
      });
      map.add(itemMarker);
      allMarkers.push(itemMarker);
      // son子点marker
      if (item.son && Array.isArray(item.son)) {
        item.son.forEach((son) => {


          const sonMarker = new AMap.Marker({
            position: new AMap.LngLat(son.longitude, son.latitude),
            offset: new AMap.Pixel(-16, -16),
            icon: new AMap.Icon({
              size: new AMap.Size(32, 32),
              image: "/icon_shop.png",
              imageSize: new AMap.Size(32, 32),
            }),
            zIndex: 100,
            title: son.name,
          });
          // 浮窗内容
          const sonInfoHtml = `
          <div class="info-card">
            <h3>${son.name}</h3>
            <p>备注：${son.remark || ""}</p>
            <p>类型：${son.specialType || ""}</p>
          </div>
        `;
          const sonInfoWindow = new AMap.InfoWindow({
            content: sonInfoHtml,
            offset: new AMap.Pixel(0, -30),
            isCustom: true,
          });
          sonMarker.on("mouseover", () => {
            sonInfoWindow.open(map, sonMarker.getPosition());
          });
          sonMarker.on("mouseout", () => {
            sonInfoWindow.close();
          });
          sonMarker.on("click", function () {
            console.log("=== 地图标记点击事件 ===");
            console.log("点击的商铺son对象:", JSON.stringify(son, null, 2));
            console.log("son.customerCode:", son.customerCode);
            console.log("son.longitude:", son.longitude);
            console.log("son.latitude:", son.latitude);
            console.log("所属父级item.name:", item.name);

            activeNames1.value = [item.name];
            longitude.value = son.longitude;
            latitude.value = son.latitude;

            console.log("设置后的全局坐标 longitude.value:", longitude.value);
            console.log("设置后的全局坐标 latitude.value:", latitude.value);

            document.getElementById(item.name)?.scrollIntoView();
            setTimeout(() => {
              const el = document.getElementById(
                "shop-" + son.longitude + "-" + son.latitude
              );
              if (el) {
                el.scrollIntoView({ behavior: "smooth", block: "center" });
              }
            }, 300);
          });
          map.add(sonMarker);
          allMarkers.push(sonMarker);
        });
      }
    });
  }

  // 2. 恢复相关变量和方法
  const isPreviewing = ref(false);

  const togglePreview = async () => {
    if (isFinished.value) {
      ElMessage.error("未加载完毕");
      return;
    }
    if (!isPreviewing.value) {
      // 进入预览
      isMaoFinished.value = true;
      await previewAdjust();
      isMaoFinished.value = false;
      isPreviewing.value = true;
    } else {
      // 取消预览，刷新页面即可
      location.reload();
    }
  };

  const oneKeyAdjust = async () => {
    if (isFinished.value) {
      ElMessage.error("未加载完毕");
      return;
    }

    if (!clusterStore.errorDepotAll || clusterStore.errorDepotAll.length === 0) {
      ElMessage.error("没有错误点需要调整");
      return;
    }

    try {
      console.log("🚀 开始一键调整，错误点数据:", clusterStore.errorDepotAll);

      let successCount = 0;
      let failCount = 0;
      const totalCount = clusterStore.errorDepotAll.length;

      ElMessage.info(`开始批量调整 ${totalCount} 个错误点...`);

      // 批量处理每个错误点
      for (let i = 0; i < clusterStore.errorDepotAll.length; i++) {
        const errorItem = clusterStore.errorDepotAll[i];

        try {
          console.log(`🔧 处理第 ${i + 1}/${totalCount} 个错误点:`, errorItem.name);

          // 获取该商铺可调整的聚集区列表
          const closestPointsRes = await clusterStore.getClosestPointsAction({
            longitude: errorItem.longitude,
            latitude: errorItem.latitude,
            customerCode: errorItem.customerCode
          });

          if (closestPointsRes && closestPointsRes.length > 0) {
            // 默认选择第一个聚集区
            const targetAccumulation = closestPointsRes[0];
            console.log(`📍 选择目标聚集区:`, targetAccumulation.accumulationName);

            // 执行微调
            const adjustRes = await clusterStore.postUpdateStoreAccumulationIdAction({
              longitude: errorItem.longitude,
              latitude: errorItem.latitude,
              accumulationId: targetAccumulation.accumulationId,
              customerCode: errorItem.customerCode
            });

            if (clusterStore.UpdateStoreAccumulationIdCode === 200) {
              successCount++;
              console.log(`✅ 第 ${i + 1} 个错误点调整成功`);
            } else {
              failCount++;
              console.log(`❌ 第 ${i + 1} 个错误点调整失败`);
            }
          } else {
            failCount++;
            console.log(`❌ 第 ${i + 1} 个错误点无可用聚集区`);
          }

          // 添加延迟避免请求过快
          await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error) {
          failCount++;
          console.error(`❌ 第 ${i + 1} 个错误点处理异常:`, error);
        }
      }

      // 显示结果
      if (successCount > 0) {
        ElMessage.success(`一键调整完成！成功: ${successCount}, 失败: ${failCount}`);
        setTimeout(() => {
          location.reload();
        }, 2000);
      } else {
        ElMessage.error(`一键调整失败！成功: ${successCount}, 失败: ${failCount}`);
      }

    } catch (e) {
      console.error("一键调整异常:", e);
      ElMessage.error("一键调整异常");
    }
  };

  // 3. 恢复previewAdjust方法（原有聚集区预览渲染逻辑）
  const previewAdjust = async () => {
    // 清除
    allMarkers.forEach((marker) => map!.remove(marker));
    allMarkers = [];
    allPolylines.forEach((line) => map!.remove(line));
    allPolylines = [];

    // 获取数据
    const res = await clusterStore.getAdjustPreviewData();
    const data = res.data || [];

    data.forEach((acc: any) => {
      // 原点 acc
      const originMarker = new AMap.Marker({
        position: new AMap.LngLat(acc.accLongitude, acc.accLatitude),
        offset: new AMap.Pixel(-16, -32),
        icon: new AMap.Icon({
          size: new AMap.Size(32, 32),
          image: mapIcon.red,
          imageSize: new AMap.Size(32, 32),
        }),
        zIndex: 200,
        title: acc.accName,
      });
      map!.add(originMarker);
      allMarkers.push(originMarker);

      // 2. 遍历所有 perviewPoints
      acc.perviewPoints.forEach((point) => {
        // oldStorePoint
        const oldStoreMarker = new AMap.Marker({
          position: new AMap.LngLat(
            point.oldStorePointLongitude,
            point.oldStorePointLatitude
          ),
          offset: new AMap.Pixel(-16, -16),
          icon: new AMap.Icon({
            size: new AMap.Size(32, 32),
            image: "/icon_shop.png",
            imageSize: new AMap.Size(32, 32),
          }),
          zIndex: 100,
          title: point.oldStorePointName,
        });
        map!.add(oldStoreMarker);
        allMarkers.push(oldStoreMarker);

        // 原点与oldStorePoint实体线
        const polyline1 = new AMap.Polyline({
          path: [
            new AMap.LngLat(Number(acc.accLongitude), Number(acc.accLatitude)),
            new AMap.LngLat(
              Number(point.oldStorePointLongitude),
              Number(point.oldStorePointLatitude)
            ),
          ],
          strokeColor: "#3078AC",
          strokeWeight: 3,
          isOutline: true,
          outlineColor: "#fff",
          borderWeight: 1,
          lineJoin: "round",
          lineCap: "round",
          zIndex: 120,
          strokeStyle: "solid",
        });
        map!.add(polyline1);
        allPolylines.push(polyline1);

        // newAccPoint
        const newAccMarker = new AMap.Marker({
          position: new AMap.LngLat(
            point.newAccPointLongitude,
            point.newAccPointLatitude
          ),
          offset: new AMap.Pixel(-16, -16),
          icon: new AMap.Icon({
            size: new AMap.Size(32, 32),
            image: mapIcon.orange,
            imageSize: new AMap.Size(32, 32),
          }),
          zIndex: 200,
          title: point.newAccPointName,
        });
        map!.add(newAccMarker);
        allMarkers.push(newAccMarker);

        // oldStorePoint与newAccPoint虚线
        const polyline2 = new AMap.Polyline({
          path: [
            new AMap.LngLat(
              Number(point.oldStorePointLongitude),
              Number(point.oldStorePointLatitude)
            ),
            new AMap.LngLat(
              Number(point.newAccPointLongitude),
              Number(point.newAccPointLatitude)
            ),
          ],
          strokeColor: "#FF9900",
          strokeWeight: 3,
          isOutline: true,
          outlineColor: "#fff",
          borderWeight: 1,
          lineJoin: "round",
          lineCap: "round",
          zIndex: 120,
          strokeStyle: "dashed",
          strokeDasharray: [10, 10],
        });
        map!.add(polyline2);
        allPolylines.push(polyline2);
      });
    });
  };
</script>
<style lang="scss" scoped>
  .AreaAdjust {
    width: 100%;
    margin-left: -2vw;
    ::v-deep(.el-collapse-item__header) {
      font-size: 16px !important;
    }
    .flex {
      width: 100%;
      display: flex;
      justify-content: center;
      ::v-deep(.el-button) {
        width: 8vw !important;
        margin: 0;
        &:nth-child(1) {
          margin-right: 2vw;
        }
      }
    }
    .back {
      font-size: 20px;
      display: flex;
      align-items: center;
      font-weight: bolder;
      color: white;
      user-select: none;
      letter-spacing: 2px;

      .backBtn {
        cursor: pointer;
      }
    }

    #container {
      padding: 0px;
      margin: 0px;
      width: 70vw;
      height: 79vh;
      margin: 0.5vh 0;
    }

    .dv-border-box-12 {
      position: absolute;
      top: 14vh;
      right: 1vw;
      width: 25vw;
      height: 80vh;

      .empty {
        width: 100%;
        height: 100%;
        float: left;
      }

      .adjustCollapse {
        height: 20vh;
        padding-left: 1vw;
        padding-top: 3vh;
        width: 85%;
        ::v-deep(.el-collapse-item__header) {
          font-size: 16px !important;
        }
        .title {
          font-size: 2.2vh;
          font-weight: bold;
          margin-bottom: 1vh;
        }
        ::v-deep(.el-collapse-item__header::before) {
          content: "";
          display: inline-block;
          width: 15px;
          height: 15px;
          background-color: #e0c340;
          border-radius: 50%;
          margin-right: 10px;
        }

        ::v-deep(.el-collapse-item__header::after) {
          // position: relative;
          margin-left: 10px;
          margin-right: -15px;
          width: 33px;
          height: 33px;
          background-color: #e0c340;
          transform: rotate(45deg);
          content: "";
          display: inline-block;
        }

        .item {
          position: absolute;
          z-index: 10;
          margin-top: 20px;
          margin-left: 20.6vw;
        }

        li {
          list-style: disc;
          margin-left: 30px;
          padding: 5px;
        }
      }
    }
  }
  .active {
    background-color: #b5a55f !important;
    padding: 0 0.5vw !important;
  }
  .adjustDialogContent {
    margin-top: 20px;

    ::v-deep(.el-collapse-item__header::before) {
      content: "";
      display: inline-block;
      width: 15px;
      height: 15px;
      background-color: #e0c340;
      border-radius: 50%;
      margin-right: 10px;
    }

    ::v-deep(.el-collapse-item__header::after) {
      margin-left: 10px;
      margin-right: -15px;
      width: 33px;
      height: 33px;
      background-color: #e0c340;
      transform: rotate(45deg);
      content: "";
      display: inline-block;
    }

    ::v-deep(.el-collapse-item__header) {
      font-size: 16px !important;
    }

    ::v-deep(.adjustDialogChange) {
      margin-top: 25px;
    }
  }

  :deep(.info-card) {
    padding: 12px;
    min-width: 180px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  }
  :deep(.info-card h3) {
    margin: 0 0 8px;
    color: #1a73e8;
    font-size: 15px;
  }
  :deep(.info-card p) {
    margin: 4px 0;
    color: #666;
    font-size: 13px;
  }
</style>
