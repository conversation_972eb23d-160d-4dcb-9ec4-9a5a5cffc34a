# Spring
spring:
  application:
    # 应用名称
    name: gateway
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      server-addr: localhost:8848
      config:
        server-addr: localhost:8848
        #namespace: b42041b6-aea5-49ba-9001-7f25fbd47328
        file-extension: yaml
      discovery:
        server-addr: localhost:8848
        #namespace: b42041b6-aea5-49ba-9001-7f25fbd47328

# 文件配置
file:
  # 文件保存路径
  savePath: /www/wwwroot/ycwl/resource/file
  # 文件访问路径前缀
  accessPathPrefix: file
    # 烟草//数据库：************
#    nacos:
#      server-addr: ************:8848
#      discovery:
#        server-addr: ************:8848
#      config:
#        server-addr: ************:8848
#        file-extension: yaml