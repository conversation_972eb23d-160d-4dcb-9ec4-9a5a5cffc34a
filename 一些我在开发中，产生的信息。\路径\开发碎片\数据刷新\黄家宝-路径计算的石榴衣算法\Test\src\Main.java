import org.locationtech.jts.geom.*;
import org.locationtech.jts.io.ParseException;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class Main {

    private static GeometryFactory geometryFactory = new GeometryFactory();

    public static boolean debug(Polygon route, Polygon edge) {
        return route != null && edge != null && edge.contains(route);
    }


    // 获取凸包
    public List<Polygon> getRoute(String filename) {
        Readroute readerRoute = new Readroute();
        List<Polygon> route = new ArrayList<>();
        try (BufferedWriter routeWriter = new BufferedWriter(new FileWriter("out.txt"))) {
            // 从指定路径读取多边形并打印
            route = readerRoute.readPolygonsFromFile(filename);
            //routeWriter.write("-------------------------打印所有凸包-------------------------\n");
            while(route.indexOf(null) != -1) {
                route.remove(null);
            }
            for (Polygon polygon : route) {
                // 打印每个多边形的WKT表示
                if (polygon != null) {
                    routeWriter.write(polygon.toText() + "\n");
                }
                else System.out.println("NOOOOOOOO!!!!");
            }
            routeWriter.write("\n\n\n\n");
        } catch (IOException | ParseException e) {
            e.printStackTrace();
        }
        return route;
    }

    // 泰森图-补集修复法(第一版, 错误多)
    public List<Polygon> getSplits(List<Polygon> route) {
        List<Polygon> splits = new ArrayList<>();
        VoronoiSplit VoronoiSplit = new VoronoiSplit();
        try (BufferedWriter splitsWriter = new BufferedWriter(new FileWriter("splits_output.txt"))) {
            if (route != null) {
                splits = VoronoiSplit.splitPolygonsWithVoronoi(route);
            }
            //splitsWriter.write("-------------------------打印泰森多边形结果-------------------------\n");
            for (Polygon p : splits) {
                splitsWriter.write(p.toText() + "\n");
            }
            splitsWriter.write("\n\n\n\n");
        } catch (IOException e) {
            e.printStackTrace();
        }
        return splits;
    }

    // 三角剖分+泰森图填充法(效果表现最优), 但是有bug
    public Geometry gettriangulation(List<Polygon> route, Polygon boundary) {
        Geometry triangulars = null;
        try (BufferedWriter splitsWriter = new BufferedWriter(new FileWriter("triangulars_output.txt"))) {
            triangulars = Triangulation.solve(route);
            triangulars = triangulars.intersection(boundary);
            //splitsWriter.write("-------------------------打印三角剖分网格结果-------------------------\n");
            splitsWriter.write(triangulars.toText() + "\n");
            splitsWriter.write("\n\n\n\n");
        } catch (IOException e) {
            e.printStackTrace();
        }
        return triangulars;
    }

    // 网格填充法, 最终版本
    public void get_net(Map<Polygon, Integer> group, Polygon boundary) {
        //Geometry triangulars = null;
        try (BufferedWriter splitsWriter = new BufferedWriter(new FileWriter("net_output.txt"))) {
            Map<Integer, List<Geometry>> merge_polygons = Networkfulling.solve(group, boundary);
            System.out.println("开始输出");
            Boolean flag = false;
            for (Map.Entry<Integer, List<Geometry>> entry : merge_polygons.entrySet()) {
                Integer groupId = entry.getKey();
                List<Geometry> block = entry.getValue();
                for(Geometry geometry : block){
                    if(geometry == null){
                        System.out.println("Have_null");
                        flag = true;
                    }
                    splitsWriter.write(geometry.toText() + "\n");
                }

                splitsWriter.write("\n\n\n\n");
            }
            if(!flag) System.out.println("No_null");
            System.out.println("输出完毕");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void text(Map<Polygon, Integer> group, Polygon boundary, List<Polygon> polygonss) {
        //Geometry triangulars = null;
        try (BufferedWriter splitsWriter = new BufferedWriter(new FileWriter("net_output.txt"))) {
            List<Polygon> polygons = Networkfulling.test(group, boundary);
            if(polygons.equals(polygonss)) System.out.println("YESSSSSS");
            else System.out.println("NOOOOOOO");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public Geometry get_net(List<Polygon> route, Polygon boundary) {
        Geometry triangulars = null;
        try (BufferedWriter splitsWriter = new BufferedWriter(new FileWriter("net_output.txt"))) {
            triangulars = Networkfulling.solve(route);
            System.out.println("开始切割边界");
            //triangulars = triangulars.intersection(boundary);
            //splitsWriter.write("-------------------------打印网格填充结果-------------------------\n");
            System.out.println("开始输出");
            //splitsWriter.write(triangulars.toText() + "\n");
            /*for(int i = 0; i < triangulars.getNumGeometries(); i++){
                System.out.println(triangulars.getGeometryN(i));
            }*/
            splitsWriter.write("\n\n\n\n");
            System.out.println("输出完毕");
        } catch (IOException e) {
            e.printStackTrace();
        }
        return triangulars;
    }

    // 获得边界
    public Polygon getBoundary(String filename) {
        Readboundary readerBoun = new Readboundary();
        Polygon boundary = null;
        try (BufferedWriter boundaryWriter = new BufferedWriter(new FileWriter("boundary_output.txt"))) {
            //boundaryWriter.write("-------------------------打印边界-------------------------\n");
            // 从指定路径读取多边形并打印
            boundary = readerBoun.readPolygonsFromFile(filename);
            // 打印每个多边形的WKT表示
            if (boundary != null) {
                boundaryWriter.write(boundary.toText() + "\n");
            }
            boundaryWriter.write("\n\n\n\n");
        } catch (IOException | ParseException e) {
            e.printStackTrace();
        }
        return boundary;
    }

    public static void main(String[] args) {
        Main main = new Main();

        // 读取路线
        //List<Polygon> route = main.getRoute("route(3).txt");
        List<Polygon> route = main.getRoute("route.txt");
        System.out.println(route);

        // 读取并打印边界
        Polygon boundary = main.getBoundary("boundary.txt");
        Map<Polygon, Integer> group = Test.To_group(route);
        // 获取并打印泰森多边形
        //main.get_net(route, boundary);
        main.get_net(group, boundary);
        //main.text(group, boundary, route);


        // 调试：检查每个多边形是否在边界内
        /*if (boundary != null) {
            for (Polygon p : splits) {
                if (debug(p, boundary)) {
                    System.out.println("Polygon is within boundary: " + p.toText());
                } else {
                    System.out.println("Polygon is outside boundary: " + p.toText());
                }
            }
        }*/
    }
}
