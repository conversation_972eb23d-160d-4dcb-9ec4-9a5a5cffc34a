package com.ict.ycwl.clustercalculate.controller;

import com.ict.ycwl.clustercalculate.pojo.*;
import com.ict.ycwl.clustercalculate.service.CalculateService;
import com.ict.ycwl.clustercalculate.service.ParameterService;
import com.ict.ycwl.clustercalculate.service.PointService;
import com.ict.ycwl.clustercalculate.service.TestInformation;
import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.user.annotation.UserLoginToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@Api(tags = "聚集区API")
@RestController
@RequestMapping("/cluster")
public class ClusterController {
    @Autowired
    private CalculateService calculateService;

    @Autowired
    private PointService pointService;

    @Autowired
    private TestInformation testInformation;

    @Autowired
    private ParameterService parameterService;

    @ApiOperation(value = "计算接口")
    @PostMapping("/calculateAll")
    @UserLoginToken
    public Object calculateAll() {
        calculateService.calculateAll();
        return AjaxResult.success("计算成功");
    }

    @ApiOperation(value = "获取地图所有商铺点接口")
    @GetMapping("/getMapResultPoints")
    public Object getMapResultPoints() {
        Object mapResultPoints = pointService.getMapResultPoints();
        return AjaxResult.success(mapResultPoints);
    }

    @ApiOperation(value = "获取右侧栏所有商铺点接口")
    @GetMapping("/getListResultPoints")
    public Object getListResultPoints() {
        List<ListPoint> listResultPoints = pointService.getListResultPoints();
        return AjaxResult.success(listResultPoints);
    }

    @ApiOperation(value = "检测所有大区的所有聚集区是否有错误点接口")
    @GetMapping("/checkErrorPoints")
    public Object checkErrorPoints() {
        int i = pointService.checkErrorPoints();
        if (i == 0) {
            return AjaxResult.success("没有错误点", i);
        } else {
            return AjaxResult.success("有错误点", i);
        }

    }

    @ApiOperation(value = "获取错误点接口")
    @GetMapping("/getErrorPoints")
    @UserLoginToken
    public Object getErrorPoints() {
        List<ListErrorCluster> errorPoints = pointService.getErrorPoints1();
        if (errorPoints.size() == 0) {
            return AjaxResult.success("没有错误点");
        } else {
            return AjaxResult.success(errorPoints);
        }
    }

    @ApiOperation(value = "获取错误点接口-分区")
    @GetMapping("/getErrorPointsPlus")
    public Object getErrorPointsPlus() {
        List<ErrorPointsByAccumulation> errorPoints = pointService.getErrorPointsByAccumulation();
        if (errorPoints.size() == 0) {
            return AjaxResult.success("没有错误点");
        } else {
            return AjaxResult.success(errorPoints);
        }
    }


    @ApiOperation(value = "获取当前商铺可调整到的聚集区接口")
    @GetMapping("/getClosestPoints")
    public Object getClosestPoints(@ApiParam(value = "当前商铺经度", required = true)
                                   @RequestParam("longitude") double longitude,
                                   @ApiParam(value = "当前商铺纬度", required = true)
                                   @RequestParam("latitude") double latitude) {
        List<Accumulation> closestPoints = pointService.getClosestPoints(longitude, latitude);
        return AjaxResult.success(closestPoints);
    }

    @ApiOperation(value = "聚集区微调接口")
    @PostMapping("/updateStoreAccumulationId")
    public Object updateStoreAccumulationId(@ApiParam(value = "当前商铺经度", required = true)
                                            @RequestParam("longitude") double longitude,
                                            @ApiParam(value = "当前商铺纬度", required = true)
                                            @RequestParam("latitude") double latitude,
                                            @ApiParam(value = "目标聚集区id", required = true)
                                            @RequestParam("accumulationId") Long accumulationId) {
        int i = pointService.updateStoreAccumulationId(longitude, latitude, accumulationId);
        if (i != 0) {
            return AjaxResult.success("操作成功");
        } else {
            return AjaxResult.success("操作失败");
        }
    }

    @ApiOperation(value = "测试增删改修改数据信息接口")
    @PostMapping("/testInformation")
    public Object testInformation(@ApiParam(value = "区域名字", required = true)
                                  @RequestParam("managerName") String managerName,
                                  @ApiParam(value = "修改信息", required = true)
                                  @RequestParam("data") String data) {
        testInformation.testInformation(managerName, data);
        return AjaxResult.success("操作成功");
    }

    @ApiOperation(value = "获取修改数据信息列表接口")
    @GetMapping("/getInformationList")
    public Object getInformationList() {
        List<ParameterListManager> informationList = parameterService.getInformationList();
        return AjaxResult.success(informationList);
    }

    @ApiOperation(value = "清空修改数据信息列表里的全部信息")
    @DeleteMapping("/clearInformationList")
    public Object clearInformationList() {
        parameterService.clearInformation();
        return AjaxResult.success("操作成功");
    }

    @ApiOperation(value = "聚集区调整预览")
    @GetMapping("/preview")
    public AjaxResult preview() {
        return pointService.perview();
    }

    @ApiOperation(value = "一键调整")
    @GetMapping("/adjustment")
    public AjaxResult adjustment() {
        AjaxResult res = pointService.adjustment();
        return res;
    }

    @ApiOperation("获取所有聚集区信息-分区")
    @GetMapping("/getAllAcc")
    public AjaxResult getAddAcc(){
        AjaxResult res=pointService.getAllAcc();
        return res;
    }


}
